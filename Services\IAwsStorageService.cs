using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public interface IAwsStorageService
    {
        Task<string> UploadFileToS3Async(Stream fileStream, string fileName, string contentType);
        Task<bool> SaveUploadMetadataAsync(UploadedFile uploadedFile);
        Task<UploadedFile?> GetUploadMetadataAsync(string uploadId);
        Task<bool> UpdateUploadStatusAsync(string uploadId, string status, string? errorMessage = null);
    }
}
