using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public interface IAwsStorageService
    {
        /// <summary>
        /// Upload a file to S3 and return the S3 key
        /// </summary>
        /// <param name="fileStream">Stream containing the file data</param>
        /// <param name="fileName">Original file name</param>
        /// <param name="contentType">MIME content type</param>
        /// <returns>S3 key of the uploaded file</returns>
        Task<string> UploadFileToS3Async(Stream fileStream, string fileName, string contentType);

        /// <summary>
        /// Save upload metadata to DynamoDB
        /// </summary>
        /// <param name="uploadedFile">Upload metadata to save</param>
        /// <returns>Success status</returns>
        Task<bool> SaveUploadMetadataAsync(UploadedFile uploadedFile);

        /// <summary>
        /// Get upload metadata from DynamoDB by upload ID
        /// </summary>
        /// <param name="uploadId">Unique upload identifier</param>
        /// <returns>Upload metadata or null if not found</returns>
        Task<UploadedFile?> GetUploadMetadataAsync(string uploadId);

        /// <summary>
        /// Update upload status in DynamoDB
        /// </summary>
        /// <param name="uploadId">Unique upload identifier</param>
        /// <param name="status">New status</param>
        /// <param name="errorMessage">Error message if status is failed</param>
        /// <returns>Success status</returns>
        Task<bool> UpdateUploadStatusAsync(string uploadId, string status, string? errorMessage = null);

        /// <summary>
        /// Get list of uploads with optional filtering
        /// </summary>
        /// <param name="status">Filter by status (optional)</param>
        /// <param name="deviceName">Filter by device name (optional)</param>
        /// <param name="limit">Maximum number of results</param>
        /// <returns>List of upload metadata</returns>
        Task<List<UploadedFile>> GetUploadsAsync(string? status = null, string? deviceName = null, int limit = 50);

        /// <summary>
        /// Delete a file from S3
        /// </summary>
        /// <param name="s3Key">S3 key of the file to delete</param>
        /// <returns>Success status</returns>
        Task<bool> DeleteFileFromS3Async(string s3Key);

        /// <summary>
        /// Generate a presigned URL for downloading a file from S3
        /// </summary>
        /// <param name="s3Key">S3 key of the file</param>
        /// <param name="expirationMinutes">URL expiration time in minutes</param>
        /// <returns>Presigned download URL</returns>
        Task<string> GenerateDownloadUrlAsync(string s3Key, int expirationMinutes = 60);
    }
}
