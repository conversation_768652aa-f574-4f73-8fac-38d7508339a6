namespace ManualSDCardUploadAPI.DTOs
{
    public class FileUploadResponseDto
    {
        public string UploadId { get; set; } = null!;
        public string FileName { get; set; } = null!;
        public long FileSizeBytes { get; set; }
        public DateTime UploadTimestamp { get; set; }
        public string S3Key { get; set; } = null!;
        public string Status { get; set; } = null!;
        public string? Description { get; set; }
        public string? DeviceName { get; set; }
        public string? DeviceModel { get; set; }
        public DateTime? CaptureDate { get; set; }
        public string? Location { get; set; }
        public int ExtractedFileCount { get; set; }
        public List<string> ExtractedFileNames { get; set; } = new();
        public Dictionary<string, string>? Metadata { get; set; }
    }
}
