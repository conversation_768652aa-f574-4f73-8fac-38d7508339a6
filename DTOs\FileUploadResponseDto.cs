namespace ManualSDCardUploadAPI.DTOs
{
    public class FileUploadResponseDto
    {
        public string UploadId { get; set; } = null!;
        public string FileName { get; set; } = null!;
        public long FileSizeBytes { get; set; }
        public DateTime UploadTimestamp { get; set; }
        public string Status { get; set; } = null!;
        public string? Description { get; set; }
        public int ExtractedFileCount { get; set; }
    }
}
