{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "ManualSDCardUploadAPI": "Information", "Amazon": "Warning"}}, "AllowedHosts": "*", "AWS": {"Profile": "default", "Region": "us-east-1", "S3": {"BucketName": "manual-sd-card-uploads", "PresignedUrlExpirationMinutes": 60}, "DynamoDB": {"TableName": "UploadedFiles"}}, "FileUpload": {"MaxFileSizeBytes": 104857600, "AllowedExtensions": [".zip"], "UploadPath": "uploads/", "MaxConcurrentUploads": 10, "ProcessingTimeoutMinutes": 30}, "Security": {"RequireHttps": true, "AllowedOrigins": [], "EnableDetailedErrors": false, "RateLimitRequestsPerMinute": 60}}