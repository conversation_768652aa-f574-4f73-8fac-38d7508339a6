using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public interface IZipProcessingService
    {
        /// <summary>
        /// Extract and analyze the contents of a zip file
        /// </summary>
        /// <param name="zipFileStream">Stream containing the zip file data</param>
        /// <param name="fileName">Original file name</param>
        /// <returns>Extraction result with file contents and metadata</returns>
        Task<ZipExtractionResult> ExtractZipFileAsync(Stream zipFileStream, string fileName);

        /// <summary>
        /// Validate zip file contents for security and format compliance
        /// </summary>
        /// <param name="extractionResult">Result from zip extraction</param>
        /// <returns>Validation result with any issues found</returns>
        Task<ValidationResult> ValidateZipContentsAsync(ZipExtractionResult extractionResult);

        /// <summary>
        /// Get summary statistics about the zip file contents
        /// </summary>
        /// <param name="extractionResult">Result from zip extraction</param>
        /// <returns>Summary statistics</returns>
        ZipContentSummary GetContentSummary(ZipExtractionResult extractionResult);
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    public class ZipContentSummary
    {
        public int TotalFiles { get; set; }
        public int TotalDirectories { get; set; }
        public long TotalSizeBytes { get; set; }
        public Dictionary<string, int> FileTypeCount { get; set; } = new();
        public List<string> LargestFiles { get; set; } = new();
        public DateTime? OldestFileDate { get; set; }
        public DateTime? NewestFileDate { get; set; }
    }
}
