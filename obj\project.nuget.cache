{"version": 2, "dgSpecHash": "el2WNyPLBic=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\Manual-SD-Card-Upload-Rest-API\\ManualSDCardUploadAPI.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\********\\awssdk.core.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.dynamodbv2\\*******\\awssdk.dynamodbv2.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.s3\\*******\\awssdk.s3.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.openapi\\9.0.6\\microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\8.0.0\\microsoft.extensions.apidescription.server.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.6.23\\microsoft.openapi.1.6.23.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\9.0.1\\swashbuckle.aspnetcore.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\9.0.1\\swashbuckle.aspnetcore.swagger.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\9.0.1\\swashbuckle.aspnetcore.swaggergen.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\9.0.1\\swashbuckle.aspnetcore.swaggerui.9.0.1.nupkg.sha512"], "logs": []}