using Microsoft.AspNetCore.Mvc;
using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Services;

namespace ManualSDCardUploadAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FileUploadController : ControllerBase
    {
        private readonly IFileUploadService _fileUploadService;

        public FileUploadController(IFileUploadService fileUploadService)
        {
            _fileUploadService = fileUploadService;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile([FromForm] FileUploadRequestDto request)
        {
            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest(ApiResponseDto<object>.ErrorResponse("No file was uploaded"));
            }

            var result = await _fileUploadService.UploadFileAsync(request);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("status/{uploadId}")]
        public async Task<IActionResult> GetUploadStatus(string uploadId)
        {
            var result = await _fileUploadService.GetUploadStatusAsync(uploadId);
            return result.Success ? Ok(result) : NotFound(result);
        }
    }
}
