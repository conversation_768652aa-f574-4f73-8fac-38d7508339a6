using Microsoft.AspNetCore.Mvc;
using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Services;
using System.ComponentModel.DataAnnotations;

namespace ManualSDCardUploadAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FileUploadController : ControllerBase
    {
        private readonly IFileUploadService _fileUploadService;
        private readonly ILogger<FileUploadController> _logger;
        private readonly IConfiguration _configuration;

        public FileUploadController(
            IFileUploadService fileUploadService,
            ILogger<FileUploadController> logger,
            IConfiguration configuration)
        {
            _fileUploadService = fileUploadService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Upload a zip file containing SD card data
        /// </summary>
        /// <param name="request">File upload request containing the zip file and metadata</param>
        /// <returns>Upload response with file details and processing status</returns>
        [HttpPost("upload")]
        [ProducesResponseType(typeof(ApiResponseDto<FileUploadResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponseDto<object>), 400)]
        [ProducesResponseType(typeof(ApiResponseDto<object>), 500)]
        public async Task<IActionResult> UploadZipFile([FromForm] FileUploadRequestDto request)
        {
            try
            {
                _logger.LogInformation("Received file upload request for file: {FileName}", request.ZipFile?.FileName);

                // Validate the request
                var validationResult = ValidateUploadRequest(request);
                if (!validationResult.IsValid)
                {
                    return BadRequest(ApiResponseDto<object>.ErrorResponse(
                        "Validation failed", validationResult.Errors));
                }

                // Process the upload
                var result = await _fileUploadService.UploadZipFileAsync(request);

                if (result.Success)
                {
                    _logger.LogInformation("File upload completed successfully. Upload ID: {UploadId}", result.Data?.UploadId);
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning("File upload failed: {Message}", result.Message);
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred during file upload");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(
                    "An internal server error occurred", ex.Message));
            }
        }

        /// <summary>
        /// Get upload status and details by upload ID
        /// </summary>
        /// <param name="uploadId">The unique upload identifier</param>
        /// <returns>Upload details and current status</returns>
        [HttpGet("status/{uploadId}")]
        [ProducesResponseType(typeof(ApiResponseDto<FileUploadResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponseDto<object>), 404)]
        [ProducesResponseType(typeof(ApiResponseDto<object>), 500)]
        public async Task<IActionResult> GetUploadStatus(string uploadId)
        {
            try
            {
                _logger.LogInformation("Retrieving upload status for ID: {UploadId}", uploadId);

                var result = await _fileUploadService.GetUploadStatusAsync(uploadId);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return NotFound(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while retrieving upload status for ID: {UploadId}", uploadId);
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(
                    "An internal server error occurred", ex.Message));
            }
        }

        /// <summary>
        /// Get a list of all uploads with optional filtering
        /// </summary>
        /// <param name="status">Filter by upload status (optional)</param>
        /// <param name="deviceName">Filter by device name (optional)</param>
        /// <param name="limit">Maximum number of results to return (default: 50)</param>
        /// <returns>List of uploads matching the criteria</returns>
        [HttpGet("list")]
        [ProducesResponseType(typeof(ApiResponseDto<List<FileUploadResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponseDto<object>), 500)]
        public async Task<IActionResult> GetUploads(
            [FromQuery] string? status = null,
            [FromQuery] string? deviceName = null,
            [FromQuery] int limit = 50)
        {
            try
            {
                _logger.LogInformation("Retrieving uploads list with filters - Status: {Status}, DeviceName: {DeviceName}, Limit: {Limit}",
                    status, deviceName, limit);

                var result = await _fileUploadService.GetUploadsAsync(status, deviceName, limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while retrieving uploads list");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(
                    "An internal server error occurred", ex.Message));
            }
        }

        private ValidationResult ValidateUploadRequest(FileUploadRequestDto request)
        {
            var errors = new List<string>();

            // Check if file is provided
            if (request.ZipFile == null || request.ZipFile.Length == 0)
            {
                errors.Add("No file was uploaded");
                return new ValidationResult { IsValid = false, Errors = errors };
            }

            // Check file extension
            var allowedExtensions = _configuration.GetSection("FileUpload:AllowedExtensions").Get<string[]>() ?? new[] { ".zip" };
            var fileExtension = Path.GetExtension(request.ZipFile.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension))
            {
                errors.Add($"File type '{fileExtension}' is not allowed. Allowed types: {string.Join(", ", allowedExtensions)}");
            }

            // Check file size
            var maxFileSize = _configuration.GetValue<long>("FileUpload:MaxFileSizeBytes", 104857600); // 100MB default
            if (request.ZipFile.Length > maxFileSize)
            {
                errors.Add($"File size ({request.ZipFile.Length:N0} bytes) exceeds maximum allowed size ({maxFileSize:N0} bytes)");
            }

            return new ValidationResult { IsValid = errors.Count == 0, Errors = errors };
        }

        private class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new();
        }
    }
}
