using Microsoft.AspNetCore.Mvc;
using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Services;

namespace ManualSDCardUploadAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FileUploadController : ControllerBase
    {
        private readonly IFileUploadService _fileUploadService;
        private readonly ILogger<FileUploadController> _logger;

        public FileUploadController(
            IFileUploadService fileUploadService,
            ILogger<FileUploadController> logger)
        {
            _fileUploadService = fileUploadService;
            _logger = logger;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadZipFile([FromForm] FileUploadRequestDto request)
        {
            try
            {
                if (request.ZipFile == null || request.ZipFile.Length == 0)
                {
                    return BadRequest(ApiResponseDto<object>.ErrorResponse("No file was uploaded"));
                }

                if (!Path.GetExtension(request.ZipFile.FileName).Equals(".zip", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(ApiResponseDto<object>.ErrorResponse("Only .zip files are allowed"));
                }

                var result = await _fileUploadService.UploadZipFileAsync(request);
                return result.Success ? Ok(result) : BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file upload");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse("Upload failed", ex.Message));
            }
        }

        [HttpGet("status/{uploadId}")]
        public async Task<IActionResult> GetUploadStatus(string uploadId)
        {
            try
            {
                var result = await _fileUploadService.GetUploadStatusAsync(uploadId);
                return result.Success ? Ok(result) : NotFound(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload status");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse("Failed to get status", ex.Message));
            }
        }
    }
}
