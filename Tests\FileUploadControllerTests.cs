using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;
using System.Text;
using Xunit;
using ManualSDCardUploadAPI.Services;
using Moq;
using ManualSDCardUploadAPI.DTOs;

namespace ManualSDCardUploadAPI.Tests
{
    public class FileUploadControllerTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public FileUploadControllerTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task HealthCheck_ReturnsHealthyStatus()
        {
            // Act
            var response = await _client.GetAsync("/health");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            Assert.Contains("Healthy", content);
        }

        [Fact]
        public async Task GetUploadStatus_WithInvalidId_ReturnsNotFound()
        {
            // Arrange
            var invalidUploadId = "invalid-id";

            // Act
            var response = await _client.GetAsync($"/api/fileupload/status/{invalidUploadId}");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task GetUploads_ReturnsSuccessResponse()
        {
            // Act
            var response = await _client.GetAsync("/api/fileupload/list");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            Assert.Contains("success", content.ToLower());
        }

        [Fact]
        public async Task UploadZipFile_WithoutFile_ReturnsBadRequest()
        {
            // Arrange
            var formData = new MultipartFormDataContent();
            formData.Add(new StringContent("Test description"), "Description");

            // Act
            var response = await _client.PostAsync("/api/fileupload/upload", formData);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task UploadZipFile_WithInvalidFileType_ReturnsBadRequest()
        {
            // Arrange
            var formData = new MultipartFormDataContent();
            var fileContent = new ByteArrayContent(Encoding.UTF8.GetBytes("fake file content"));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
            formData.Add(fileContent, "ZipFile", "test.txt");
            formData.Add(new StringContent("Test description"), "Description");

            // Act
            var response = await _client.PostAsync("/api/fileupload/upload", formData);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
            var content = await response.Content.ReadAsStringAsync();
            Assert.Contains("not allowed", content);
        }
    }

    public class ZipProcessingServiceTests
    {
        [Fact]
        public void GetContentSummary_WithEmptyResult_ReturnsZeroValues()
        {
            // Arrange
            var service = new ZipProcessingService(
                Mock.Of<ILogger<ZipProcessingService>>(),
                Mock.Of<IConfiguration>());
            
            var extractionResult = new Models.ZipExtractionResult
            {
                Success = true,
                ExtractedFiles = new List<Models.ZipFileContent>(),
                FileCount = 0,
                TotalSizeBytes = 0
            };

            // Act
            var summary = service.GetContentSummary(extractionResult);

            // Assert
            Assert.Equal(0, summary.TotalFiles);
            Assert.Equal(0, summary.TotalDirectories);
            Assert.Equal(0, summary.TotalSizeBytes);
            Assert.Empty(summary.FileTypeCount);
            Assert.Empty(summary.LargestFiles);
        }

        [Fact]
        public void GetContentSummary_WithMixedFiles_ReturnsCorrectCounts()
        {
            // Arrange
            var service = new ZipProcessingService(
                Mock.Of<ILogger<ZipProcessingService>>(),
                Mock.Of<IConfiguration>());
            
            var extractionResult = new Models.ZipExtractionResult
            {
                Success = true,
                ExtractedFiles = new List<Models.ZipFileContent>
                {
                    new() { FileName = "file1.jpg", IsDirectory = false, FileExtension = ".jpg", FileSizeBytes = 1000 },
                    new() { FileName = "file2.txt", IsDirectory = false, FileExtension = ".txt", FileSizeBytes = 500 },
                    new() { FileName = "folder1", IsDirectory = true, FileExtension = "", FileSizeBytes = 0 },
                    new() { FileName = "file3.jpg", IsDirectory = false, FileExtension = ".jpg", FileSizeBytes = 2000 }
                },
                FileCount = 4,
                TotalSizeBytes = 3500
            };

            // Act
            var summary = service.GetContentSummary(extractionResult);

            // Assert
            Assert.Equal(3, summary.TotalFiles);
            Assert.Equal(1, summary.TotalDirectories);
            Assert.Equal(3500, summary.TotalSizeBytes);
            Assert.Equal(2, summary.FileTypeCount[".jpg"]);
            Assert.Equal(1, summary.FileTypeCount[".txt"]);
        }
    }
}
