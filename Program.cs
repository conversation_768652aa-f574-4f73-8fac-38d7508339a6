using Amazon.S3;
using Amazon.DynamoDBv2;
using ManualSDCardUploadAPI.Services;
using ManualSDCardUploadAPI.Models;
using Microsoft.AspNetCore.Http.Features;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Bind configuration sections
builder.Services.Configure<AwsConfiguration>(builder.Configuration.GetSection("AWS"));
builder.Services.Configure<FileUploadConfiguration>(builder.Configuration.GetSection("FileUpload"));
builder.Services.Configure<SecurityConfiguration>(builder.Configuration.GetSection("Security"));

// Add services to the container.
builder.Services.AddControllers();

// Configure CORS
var securityConfig = builder.Configuration.GetSection("Security").Get<SecurityConfiguration>() ?? new SecurityConfiguration();
if (securityConfig.AllowedOrigins.Any())
{
    builder.Services.AddCors(options =>
    {
        options.AddDefaultPolicy(policy =>
        {
            policy.WithOrigins(securityConfig.AllowedOrigins)
                  .AllowAnyMethod()
                  .AllowAnyHeader();
        });
    });
}

// Configure AWS services
builder.Services.AddAWSService<IAmazonS3>();
builder.Services.AddAWSService<IAmazonDynamoDB>();

// Register custom services
builder.Services.AddScoped<IFileUploadService, FileUploadService>();
builder.Services.AddScoped<IZipProcessingService, ZipProcessingService>();
builder.Services.AddScoped<IAwsStorageService, AwsStorageService>();

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "Manual SD Card Upload API",
        Version = "v1",
        Description = "API for uploading and processing SD card zip files to AWS storage"
    });

    // Include XML comments if available
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Configure file upload limits
var fileUploadConfig = builder.Configuration.GetSection("FileUpload").Get<FileUploadConfiguration>() ?? new FileUploadConfiguration();
builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = fileUploadConfig.MaxFileSizeBytes;
    options.ValueLengthLimit = int.MaxValue;
    options.ValueCountLimit = int.MaxValue;
    options.KeyLengthLimit = int.MaxValue;
});

builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = fileUploadConfig.MaxFileSizeBytes;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Manual SD Card Upload API V1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
        c.DisplayRequestDuration();
    });
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

// Use CORS if configured
if (securityConfig.AllowedOrigins.Any())
{
    app.UseCors();
}

// Security headers
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});

if (securityConfig.RequireHttps)
{
    app.UseHttpsRedirection();
}

app.UseRouting();
app.UseAuthorization();

// Add health check endpoint
app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.UtcNow });

app.MapControllers();

app.Run();
