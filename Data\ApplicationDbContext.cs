using Microsoft.EntityFrameworkCore;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<UploadedFile> UploadedFiles { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<UploadedFile>(entity =>
            {
                entity.HasKey(e => e.UploadId);
                entity.Property(e => e.UploadId).HasMaxLength(50);
                entity.Property(e => e.FileName).HasMaxLength(255).IsRequired();
                entity.Property(e => e.S3Key).HasMaxLength(500).IsRequired();
            });
        }
    }
}
