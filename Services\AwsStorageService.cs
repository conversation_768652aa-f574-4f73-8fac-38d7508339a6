using Amazon.S3;
using Amazon.S3.Model;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class AwsStorageService : IAwsStorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly DynamoDBContext _dynamoDbContext;
        private readonly ILogger<AwsStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;
        private readonly string _tableName;

        public AwsStorageService(
            IAmazonS3 s3Client,
            IAmazonDynamoDB dynamoDbClient,
            ILogger<AwsStorageService> logger,
            IConfiguration configuration)
        {
            _s3Client = s3Client;
            _dynamoDbClient = dynamoDbClient;
            _dynamoDbContext = new DynamoDBContext(_dynamoDbClient);
            _logger = logger;
            _configuration = configuration;
            _bucketName = _configuration["AWS:S3:BucketName"] ?? "manual-sd-card-uploads";
            _tableName = _configuration["AWS:DynamoDB:TableName"] ?? "UploadedFiles";
        }

        public async Task<string> UploadFileToS3Async(Stream fileStream, string fileName, string contentType)
        {
            try
            {
                var s3Key = $"uploads/{DateTime.UtcNow:yyyy/MM/dd}/{Guid.NewGuid()}/{fileName}";
                
                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = s3Key,
                    InputStream = fileStream,
                    ContentType = contentType,
                    ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                    Metadata =
                    {
                        ["original-filename"] = fileName,
                        ["upload-timestamp"] = DateTime.UtcNow.ToString("O")
                    }
                };

                _logger.LogInformation("Uploading file to S3: {S3Key}", s3Key);
                
                var response = await _s3Client.PutObjectAsync(request);
                
                if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    _logger.LogInformation("Successfully uploaded file to S3: {S3Key}", s3Key);
                    return s3Key;
                }
                else
                {
                    throw new Exception($"S3 upload failed with status code: {response.HttpStatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task<bool> SaveUploadMetadataAsync(UploadedFile uploadedFile)
        {
            try
            {
                _logger.LogInformation("Saving upload metadata to DynamoDB: {UploadId}", uploadedFile.UploadId);
                
                await _dynamoDbContext.SaveAsync(uploadedFile);
                
                _logger.LogInformation("Successfully saved upload metadata: {UploadId}", uploadedFile.UploadId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving upload metadata: {UploadId}", uploadedFile.UploadId);
                return false;
            }
        }

        public async Task<UploadedFile?> GetUploadMetadataAsync(string uploadId)
        {
            try
            {
                _logger.LogInformation("Retrieving upload metadata from DynamoDB: {UploadId}", uploadId);
                
                var uploadedFile = await _dynamoDbContext.LoadAsync<UploadedFile>(uploadId);
                
                if (uploadedFile != null)
                {
                    _logger.LogInformation("Successfully retrieved upload metadata: {UploadId}", uploadId);
                }
                else
                {
                    _logger.LogWarning("Upload metadata not found: {UploadId}", uploadId);
                }
                
                return uploadedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload metadata: {UploadId}", uploadId);
                return null;
            }
        }

        public async Task<bool> UpdateUploadStatusAsync(string uploadId, string status, string? errorMessage = null)
        {
            try
            {
                _logger.LogInformation("Updating upload status: {UploadId} -> {Status}", uploadId, status);

                var uploadedFile = await _dynamoDbContext.LoadAsync<UploadedFile>(uploadId);
                if (uploadedFile == null)
                {
                    _logger.LogWarning("Cannot update status - upload not found: {UploadId}", uploadId);
                    return false;
                }

                uploadedFile.Status = status;
                uploadedFile.ErrorMessage = errorMessage;

                if (status == "Processing")
                {
                    uploadedFile.ProcessingStartTime = DateTime.UtcNow;
                }
                else if (status == "Completed" || status == "Failed")
                {
                    uploadedFile.ProcessingEndTime = DateTime.UtcNow;
                }

                await _dynamoDbContext.SaveAsync(uploadedFile);

                _logger.LogInformation("Successfully updated upload status: {UploadId}", uploadId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating upload status: {UploadId}", uploadId);
                return false;
            }
        }

        public async Task<List<UploadedFile>> GetUploadsAsync(string? status = null, string? deviceName = null, int limit = 50)
        {
            try
            {
                _logger.LogInformation("Retrieving uploads list with filters - Status: {Status}, DeviceName: {DeviceName}, Limit: {Limit}",
                    status, deviceName, limit);

                var scanConditions = new List<ScanCondition>();

                if (!string.IsNullOrEmpty(status))
                {
                    scanConditions.Add(new ScanCondition("Status", ScanOperator.Equal, status));
                }

                if (!string.IsNullOrEmpty(deviceName))
                {
                    scanConditions.Add(new ScanCondition("DeviceName", ScanOperator.Equal, deviceName));
                }

                var search = _dynamoDbContext.ScanAsync<UploadedFile>(scanConditions.ToArray());
                var uploads = await search.GetRemainingAsync();

                // Apply limit and sort by upload timestamp (newest first)
                var result = uploads
                    .OrderByDescending(u => u.UploadTimestamp)
                    .Take(limit)
                    .ToList();

                _logger.LogInformation("Retrieved {Count} uploads", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving uploads list");
                return new List<UploadedFile>();
            }
        }

        public async Task<bool> DeleteFileFromS3Async(string s3Key)
        {
            try
            {
                _logger.LogInformation("Deleting file from S3: {S3Key}", s3Key);

                var request = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = s3Key
                };

                var response = await _s3Client.DeleteObjectAsync(request);

                if (response.HttpStatusCode == System.Net.HttpStatusCode.NoContent)
                {
                    _logger.LogInformation("Successfully deleted file from S3: {S3Key}", s3Key);
                    return true;
                }
                else
                {
                    _logger.LogWarning("S3 delete returned unexpected status code: {StatusCode}", response.HttpStatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {S3Key}", s3Key);
                return false;
            }
        }

        public async Task<string> GenerateDownloadUrlAsync(string s3Key, int expirationMinutes = 60)
        {
            try
            {
                _logger.LogInformation("Generating presigned URL for S3 key: {S3Key}", s3Key);

                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = s3Key,
                    Verb = HttpVerb.GET,
                    Expires = DateTime.UtcNow.AddMinutes(expirationMinutes)
                };

                var url = await _s3Client.GetPreSignedURLAsync(request);

                _logger.LogInformation("Generated presigned URL for S3 key: {S3Key}", s3Key);
                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned URL for S3 key: {S3Key}", s3Key);
                throw;
            }
        }
    }
}
