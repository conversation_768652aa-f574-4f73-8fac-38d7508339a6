using Amazon.S3;
using Amazon.S3.Model;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class AwsStorageService : IAwsStorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly DynamoDBContext _dynamoDbContext;
        private readonly ILogger<AwsStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;
        private readonly string _tableName;

        public AwsStorageService(
            IAmazonS3 s3Client,
            IAmazonDynamoDB dynamoDbClient,
            ILogger<AwsStorageService> logger,
            IConfiguration configuration)
        {
            _s3Client = s3Client;
            _dynamoDbClient = dynamoDbClient;
            _dynamoDbContext = new DynamoDBContext(_dynamoDbClient);
            _logger = logger;
            _configuration = configuration;
            _bucketName = _configuration["AWS:S3:BucketName"] ?? "manual-sd-card-uploads";
            _tableName = _configuration["AWS:DynamoDB:TableName"] ?? "UploadedFiles";
        }

        public async Task<string> UploadFileToS3Async(Stream fileStream, string fileName, string contentType)
        {
            try
            {
                var s3Key = $"uploads/{DateTime.UtcNow:yyyy/MM/dd}/{Guid.NewGuid()}/{fileName}";
                
                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = s3Key,
                    InputStream = fileStream,
                    ContentType = contentType,
                    ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                    Metadata =
                    {
                        ["original-filename"] = fileName,
                        ["upload-timestamp"] = DateTime.UtcNow.ToString("O")
                    }
                };

                _logger.LogInformation("Uploading file to S3: {S3Key}", s3Key);
                
                var response = await _s3Client.PutObjectAsync(request);
                
                if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    _logger.LogInformation("Successfully uploaded file to S3: {S3Key}", s3Key);
                    return s3Key;
                }
                else
                {
                    throw new Exception($"S3 upload failed with status code: {response.HttpStatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task<bool> SaveUploadMetadataAsync(UploadedFile uploadedFile)
        {
            try
            {
                _logger.LogInformation("Saving upload metadata to DynamoDB: {UploadId}", uploadedFile.UploadId);
                
                await _dynamoDbContext.SaveAsync(uploadedFile);
                
                _logger.LogInformation("Successfully saved upload metadata: {UploadId}", uploadedFile.UploadId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving upload metadata: {UploadId}", uploadedFile.UploadId);
                return false;
            }
        }

        public async Task<UploadedFile?> GetUploadMetadataAsync(string uploadId)
        {
            try
            {
                _logger.LogInformation("Retrieving upload metadata from DynamoDB: {UploadId}", uploadId);
                
                var uploadedFile = await _dynamoDbContext.LoadAsync<UploadedFile>(uploadId);
                
                if (uploadedFile != null)
                {
                    _logger.LogInformation("Successfully retrieved upload metadata: {UploadId}", uploadId);
                }
                else
                {
                    _logger.LogWarning("Upload metadata not found: {UploadId}", uploadId);
                }
                
                return uploadedFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload metadata: {UploadId}", uploadId);
                return null;
            }
        }

        public async Task<bool> UpdateUploadStatusAsync(string uploadId, string status, string? errorMessage = null)
        {
            try
            {
                _logger.LogInformation("Updating upload status: {UploadId} -> {Status}", uploadId, status);

                var uploadedFile = await _dynamoDbContext.LoadAsync<UploadedFile>(uploadId);
                if (uploadedFile == null)
                {
                    _logger.LogWarning("Cannot update status - upload not found: {UploadId}", uploadId);
                    return false;
                }

                uploadedFile.Status = status;
                uploadedFile.ErrorMessage = errorMessage;

                if (status == "Processing")
                {
                    uploadedFile.ProcessingStartTime = DateTime.UtcNow;
                }
                else if (status == "Completed" || status == "Failed")
                {
                    uploadedFile.ProcessingEndTime = DateTime.UtcNow;
                }

                await _dynamoDbContext.SaveAsync(uploadedFile);

                _logger.LogInformation("Successfully updated upload status: {UploadId}", uploadId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating upload status: {UploadId}", uploadId);
                return false;
            }
        }


    }
}
