using Amazon.S3;
using Amazon.S3.Model;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class AwsStorageService : IAwsStorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly DynamoDBContext _dynamoDbContext;
        private readonly ILogger<AwsStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;
        private readonly string _tableName;

        public AwsStorageService(
            IAmazonS3 s3Client,
            IAmazonDynamoDB dynamoDbClient,
            ILogger<AwsStorageService> logger,
            IConfiguration configuration)
        {
            _s3Client = s3Client;
            _dynamoDbClient = dynamoDbClient;
            _dynamoDbContext = new DynamoDBContextBuilder()
                .WithDynamoDBClient(() => _dynamoDbClient)
                .Build();
            _logger = logger;
            _configuration = configuration;
            _bucketName = _configuration["AWS:S3:BucketName"] ?? "manual-sd-card-uploads";
            _tableName = _configuration["AWS:DynamoDB:TableName"] ?? "UploadedFiles";
        }

        public async Task<string> UploadFileToS3Async(Stream fileStream, string fileName)
        {
            var s3Key = $"uploads/{Guid.NewGuid()}/{fileName}";

            var request = new PutObjectRequest
            {
                BucketName = _bucketName,
                Key = s3Key,
                InputStream = fileStream
            };

            await _s3Client.PutObjectAsync(request);
            return s3Key;
        }

        public async Task<bool> SaveUploadMetadataAsync(UploadedFile uploadedFile)
        {
            await _dynamoDbContext.SaveAsync(uploadedFile);
            return true;
        }

        public async Task<UploadedFile?> GetUploadMetadataAsync(string uploadId)
        {
            return await _dynamoDbContext.LoadAsync<UploadedFile>(uploadId);
        }


    }
}
