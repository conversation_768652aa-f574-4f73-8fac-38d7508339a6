using Amazon.S3;
using Amazon.S3.Model;
using ManualSDCardUploadAPI.Models;
using ManualSDCardUploadAPI.Data;
using Microsoft.EntityFrameworkCore;

namespace ManualSDCardUploadAPI.Services
{
    public class AwsStorageService : IAwsStorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<AwsStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;

        public AwsStorageService(
            IAmazonS3 s3Client,
            ApplicationDbContext dbContext,
            ILogger<AwsStorageService> logger,
            IConfiguration configuration)
        {
            _s3Client = s3Client;
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
            _bucketName = _configuration["AWS:S3:BucketName"] ?? "manual-sd-card-uploads";
        }

        public async Task<string> UploadFileToS3Async(Stream fileStream, string fileName)
        {
            var s3Key = $"uploads/{Guid.NewGuid()}/{fileName}";

            var request = new PutObjectRequest
            {
                BucketName = _bucketName,
                Key = s3Key,
                InputStream = fileStream
            };

            await _s3Client.PutObjectAsync(request);
            return s3Key;
        }

        public async Task<bool> SaveUploadMetadataAsync(UploadedFile uploadedFile)
        {
            _dbContext.UploadedFiles.Add(uploadedFile);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<UploadedFile?> GetUploadMetadataAsync(string uploadId)
        {
            return await _dbContext.UploadedFiles.FindAsync(uploadId);
        }


    }
}
