namespace ManualSDCardUploadAPI.Models
{
    public class ZipFileContent
    {
        public string FileName { get; set; } = null!;
        public long FileSizeBytes { get; set; }
        public string FileExtension { get; set; } = null!;
        public DateTime? LastModified { get; set; }
        public string RelativePath { get; set; } = null!;
        public bool IsDirectory { get; set; }
        public byte[]? FileContent { get; set; }
    }

    public class ZipExtractionResult
    {
        public bool Success { get; set; }
        public List<ZipFileContent> ExtractedFiles { get; set; } = new();
        public long TotalSizeBytes { get; set; }
        public int FileCount { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Warnings { get; set; } = new();
    }
}
