using ManualSDCardUploadAPI.DTOs;

namespace ManualSDCardUploadAPI.Services
{
    public interface IFileUploadService
    {
        /// <summary>
        /// Process a zip file upload request
        /// </summary>
        /// <param name="request">File upload request containing the zip file and metadata</param>
        /// <returns>Upload response with processing results</returns>
        Task<ApiResponseDto<FileUploadResponseDto>> UploadZipFileAsync(FileUploadRequestDto request);

        /// <summary>
        /// Get upload status and details by upload ID
        /// </summary>
        /// <param name="uploadId">Unique upload identifier</param>
        /// <returns>Upload details and current status</returns>
        Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId);

        /// <summary>
        /// Get list of uploads with optional filtering
        /// </summary>
        /// <param name="status">Filter by upload status (optional)</param>
        /// <param name="deviceName">Filter by device name (optional)</param>
        /// <param name="limit">Maximum number of results to return</param>
        /// <returns>List of uploads matching the criteria</returns>
        Task<ApiResponseDto<List<FileUploadResponseDto>>> GetUploadsAsync(string? status = null, string? deviceName = null, int limit = 50);
    }
}
