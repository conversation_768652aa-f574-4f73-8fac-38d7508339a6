using ManualSDCardUploadAPI.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace ManualSDCardUploadAPI.Services
{
    public interface IFileUploadService
    {
        Task<ApiResponseDto<FileUploadResponseDto>> UploadFileAsync(FileUploadRequestDto request);
        Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId);
        Task<IActionResult> DownloadFileAsync(string uploadId);
    }
}
