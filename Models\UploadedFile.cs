using Amazon.DynamoDBv2.DataModel;

namespace ManualSDCardUploadAPI.Models
{
    [DynamoDBTable("UploadedFiles")]
    public class UploadedFile
    {
        [DynamoDBHashKey]
        public string UploadId { get; set; } = null!;

        [DynamoDBProperty]
        public string FileName { get; set; } = null!;

        [DynamoDBProperty]
        public long FileSizeBytes { get; set; }

        [DynamoDBProperty]
        public DateTime UploadTimestamp { get; set; }

        [DynamoDBProperty]
        public string S3Key { get; set; } = null!;

        [DynamoDBProperty]
        public string S3Bucket { get; set; } = null!;

        [DynamoDBProperty]
        public string Status { get; set; } = null!; // "Uploaded", "Processing", "Completed", "Failed"

        [DynamoDBProperty]
        public string? Description { get; set; }

        [DynamoDBProperty]
        public string? DeviceName { get; set; }

        [DynamoDBProperty]
        public string? DeviceModel { get; set; }

        [DynamoDBProperty]
        public DateTime? CaptureDate { get; set; }

        [DynamoDBProperty]
        public string? Location { get; set; }

        [DynamoDBProperty]
        public int ExtractedFileCount { get; set; }

        [DynamoDBProperty]
        public List<string> ExtractedFileNames { get; set; } = new();

        [DynamoDBProperty]
        public Dictionary<string, string>? Metadata { get; set; }

        [DynamoDBProperty]
        public DateTime? ProcessingStartTime { get; set; }

        [DynamoDBProperty]
        public DateTime? ProcessingEndTime { get; set; }

        [DynamoDBProperty]
        public string? ErrorMessage { get; set; }
    }
}
