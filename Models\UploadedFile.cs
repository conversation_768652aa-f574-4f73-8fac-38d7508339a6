using Amazon.DynamoDBv2.DataModel;

namespace ManualSDCardUploadAPI.Models
{
    [DynamoDBTable("UploadedFiles")]
    public class UploadedFile
    {
        [DynamoDBHashKey]
        public string UploadId { get; set; } = null!;

        [DynamoDBProperty]
        public string FileName { get; set; } = null!;

        [DynamoDBProperty]
        public long FileSizeBytes { get; set; }

        [DynamoDBProperty]
        public DateTime UploadTimestamp { get; set; }

        [DynamoDBProperty]
        public string S3Key { get; set; } = null!;

        [DynamoDBProperty]
        public string Status { get; set; } = null!; // "Uploaded", "Processing", "Completed", "Failed"

        [DynamoDBProperty]
        public string? Description { get; set; }

        [DynamoDBProperty]
        public int ExtractedFileCount { get; set; }

        [DynamoDBProperty]
        public string? ErrorMessage { get; set; }
    }
}
