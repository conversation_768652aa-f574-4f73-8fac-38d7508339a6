# File Upload API - Ultra Simple MVP

Upload any file to SQL Server and get an upload ID. That's it.

## What it does

1. Upload file to SQL Server database (as binary data)
2. Store upload ID + filename + file data + metadata
3. Return upload ID
4. Download files by upload ID

## Setup

```bash
git clone <repository-url>
cd Manual-SD-Card-Upload-Rest-API
dotnet restore
```

Update `appsettings.json` with your SQL Server connection:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-server;Database=FileUploadDB;User Id=username;Password=password;TrustServerCertificate=true;"
  }
}
```

Create database table:
```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

Run:
```bash
dotnet run
```

## API

**Upload file:**
```bash
curl -X POST "https://localhost:5001/api/fileupload/upload" \
  -F "File=@/path/to/any-file.txt"
```

**Get upload info:**
```bash
curl -X GET "https://localhost:5001/api/fileupload/status/{uploadId}"
```

**Download file:**
```bash
curl -X GET "https://localhost:5001/api/fileupload/download/{uploadId}" \
  --output downloaded-file.txt
```

That's it. No AWS needed, just SQL Server.

