# Manual SD Card Upload REST API

A C# .NET Web API for uploading and processing SD card zip files to AWS storage. This API accepts zip files, extracts and validates their contents, stores them in AWS S3, and maintains metadata in DynamoDB.

## Features

- **Secure File Upload**: Accept zip files with validation and security checks
- **AWS Integration**: Store files in S3 and metadata in DynamoDB
- **Zip Processing**: Extract and analyze zip file contents with security validation
- **Background Processing**: Asynchronous zip file processing
- **RESTful API**: Clean REST endpoints with comprehensive error handling
- **Swagger Documentation**: Interactive API documentation
- **Configurable**: Flexible configuration for different environments

## Prerequisites

- .NET 8.0 or later
- AWS Account with S3 and DynamoDB access
- AWS CLI configured or AWS credentials set up

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd Manual-SD-Card-Upload-Rest-API
dotnet restore
```

### 2. Configure AWS

Set up your AWS credentials using one of these methods:

**Option A: AWS CLI**
```bash
aws configure
```

**Option B: Environment Variables**
```bash
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-east-1
```

**Option C: IAM Roles** (recommended for production)

### 3. Create AWS Resources

**S3 Bucket:**
```bash
aws s3 mb s3://manual-sd-card-uploads
```

**DynamoDB Table:**
```bash
aws dynamodb create-table \
    --table-name UploadedFiles \
    --attribute-definitions AttributeName=UploadId,AttributeType=S \
    --key-schema AttributeName=UploadId,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST
```

### 4. Update Configuration

Edit `appsettings.json` or `appsettings.Development.json`:

```json
{
  "AWS": {
    "Region": "us-east-1",
    "S3": {
      "BucketName": "your-bucket-name"
    },
    "DynamoDB": {
      "TableName": "UploadedFiles"
    }
  }
}
```

### 5. Run the Application

```bash
dotnet run
```

The API will be available at `https://localhost:5001` (or the port shown in the console).

## API Endpoints

### Upload Zip File
```http
POST /api/fileupload/upload
Content-Type: multipart/form-data

Form Data:
- ZipFile: (file) The zip file to upload
- Description: (string, optional) Description of the upload
- DeviceName: (string, optional) Name of the device
- DeviceModel: (string, optional) Model of the device
- CaptureDate: (datetime, optional) When the data was captured
- Location: (string, optional) Location where data was captured
```

**Example Response:**
```json
{
  "success": true,
  "message": "File uploaded successfully. Processing started in background.",
  "data": {
    "uploadId": "123e4567-e89b-12d3-a456-426614174000",
    "fileName": "sdcard-data.zip",
    "fileSizeBytes": 1048576,
    "uploadTimestamp": "2024-01-15T10:30:00Z",
    "s3Key": "uploads/2024/01/15/123e4567-e89b-12d3-a456-426614174000/sdcard-data.zip",
    "status": "Uploaded"
  }
}
```

### Get Upload Status
```http
GET /api/fileupload/status/{uploadId}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "uploadId": "123e4567-e89b-12d3-a456-426614174000",
    "fileName": "sdcard-data.zip",
    "status": "Completed",
    "extractedFileCount": 25,
    "extractedFileNames": ["photo1.jpg", "photo2.jpg", "data.txt"]
  }
}
```

### List Uploads
```http
GET /api/fileupload/list?status=Completed&deviceName=Camera1&limit=10
```

## Configuration

### File Upload Settings
```json
{
  "FileUpload": {
    "MaxFileSizeBytes": 104857600,
    "AllowedExtensions": [".zip"],
    "MaxConcurrentUploads": 10,
    "ProcessingTimeoutMinutes": 30
  }
}
```

### Security Settings
```json
{
  "Security": {
    "RequireHttps": true,
    "AllowedOrigins": ["https://yourdomain.com"],
    "EnableDetailedErrors": false,
    "RateLimitRequestsPerMinute": 60
  }
}
```

## Usage Examples

### cURL Examples

**Upload a zip file:**
```bash
curl -X POST "https://localhost:5001/api/fileupload/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "ZipFile=@/path/to/your/file.zip" \
  -F "Description=SD card backup from Camera 1" \
  -F "DeviceName=Camera1" \
  -F "DeviceModel=Canon EOS R5"
```

**Check upload status:**
```bash
curl -X GET "https://localhost:5001/api/fileupload/status/123e4567-e89b-12d3-a456-426614174000"
```

**List uploads:**
```bash
curl -X GET "https://localhost:5001/api/fileupload/list?status=Completed&limit=5"
```

### PowerShell Examples

**Upload a zip file:**
```powershell
$uri = "https://localhost:5001/api/fileupload/upload"
$filePath = "C:\path\to\your\file.zip"

$form = @{
    ZipFile = Get-Item -Path $filePath
    Description = "SD card backup from Camera 1"
    DeviceName = "Camera1"
    DeviceModel = "Canon EOS R5"
}

Invoke-RestMethod -Uri $uri -Method Post -Form $form
```

## Status Values

- **Uploaded**: File has been uploaded to S3, processing not started
- **Processing**: Zip file is being extracted and analyzed
- **Completed**: Processing finished successfully
- **Failed**: An error occurred during upload or processing

## Security Features

- **File Type Validation**: Only allows specified file extensions
- **File Size Limits**: Configurable maximum file size
- **Zip Bomb Protection**: Prevents extraction of maliciously large archives
- **Path Traversal Protection**: Prevents directory traversal attacks
- **Content Validation**: Scans for potentially dangerous file types
- **HTTPS Enforcement**: Configurable HTTPS requirement
- **CORS Configuration**: Configurable cross-origin resource sharing

## Error Handling

The API returns standardized error responses:

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "File type '.exe' is not allowed. Allowed types: .zip",
    "File size (150000000 bytes) exceeds maximum allowed size (104857600 bytes)"
  ]
}
```

## Deployment

### Docker Deployment

Create a `Dockerfile`:
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["ManualSDCardUploadAPI.csproj", "."]
RUN dotnet restore "ManualSDCardUploadAPI.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "ManualSDCardUploadAPI.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ManualSDCardUploadAPI.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ManualSDCardUploadAPI.dll"]
```

Build and run:
```bash
docker build -t manual-sd-card-api .
docker run -p 8080:80 -e AWS_ACCESS_KEY_ID=your_key -e AWS_SECRET_ACCESS_KEY=your_secret manual-sd-card-api
```

### AWS Deployment

For production deployment on AWS, consider:

1. **AWS App Runner**: Easy container deployment
2. **AWS ECS**: Container orchestration
3. **AWS Lambda**: Serverless deployment (may require modifications)
4. **AWS Elastic Beanstalk**: Platform-as-a-service deployment

## Monitoring and Logging

The API includes structured logging. Key log events:

- File upload start/completion
- Zip extraction progress
- AWS service interactions
- Error conditions

Configure log levels in `appsettings.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "ManualSDCardUploadAPI": "Debug",
      "Amazon": "Warning"
    }
  }
}
```

## Troubleshooting

### Common Issues

**1. AWS Credentials Not Found**
```
Error: Unable to get IAM security credentials from EC2 Instance Metadata Service
```
Solution: Configure AWS credentials using AWS CLI, environment variables, or IAM roles.

**2. S3 Bucket Access Denied**
```
Error: Access Denied when uploading to S3
```
Solution: Ensure your AWS credentials have S3 write permissions for the specified bucket.

**3. DynamoDB Table Not Found**
```
Error: Requested resource not found
```
Solution: Create the DynamoDB table or verify the table name in configuration.

**4. File Size Limit Exceeded**
```
Error: Request body too large
```
Solution: Increase the file size limits in configuration or reduce the file size.

### Health Check

The API includes a health check endpoint:
```http
GET /health
```

Response:
```json
{
  "status": "Healthy",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.