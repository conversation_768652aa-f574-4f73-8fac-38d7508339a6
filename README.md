# File Upload API - Ultra Simple MVP

Upload any file to AWS S3 and get an upload ID. That's it.

## What it does

1. Upload file to S3
2. Store upload ID + filename + S3 key in DynamoDB
3. Return upload ID

## Setup

```bash
git clone <repository-url>
cd Manual-SD-Card-Upload-Rest-API
dotnet restore
aws configure
```

Create S3 bucket:
```bash
aws s3 mb s3://manual-sd-card-uploads
```

Create DynamoDB table:
```bash
aws dynamodb create-table \
    --table-name UploadedFiles \
    --attribute-definitions AttributeName=UploadId,AttributeType=S \
    --key-schema AttributeName=UploadId,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST
```

Run:
```bash
dotnet run
```

## API

**Upload file:**
```bash
curl -X POST "https://localhost:5001/api/fileupload/upload" \
  -F "File=@/path/to/any-file.txt"
```

**Get upload info:**
```bash
curl -X GET "https://localhost:5001/api/fileupload/status/{uploadId}"
```

That's it. No validation, no processing, just upload and store.

