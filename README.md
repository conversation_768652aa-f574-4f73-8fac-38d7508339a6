# Manual SD Card Upload REST API - MVP

A simple C# .NET Web API for uploading zip files to AWS S3 and storing metadata in DynamoDB.

## Features

- Upload zip files to AWS S3
- Store file metadata in DynamoDB
- Basic zip file processing (file count)
- Simple REST API with Swagger documentation

## Prerequisites

- .NET 8.0 or later
- AWS Account with S3 and DynamoDB access
- AWS CLI configured or AWS credentials set up

## Quick Start

### 1. Setup

```bash
git clone <repository-url>
cd Manual-SD-Card-Upload-Rest-API
dotnet restore
```

### 2. Configure AWS

```bash
aws configure
```

### 3. Create AWS Resources

**S3 Bucket:**
```bash
aws s3 mb s3://manual-sd-card-uploads
```

**DynamoDB Table:**
```bash
aws dynamodb create-table \
    --table-name UploadedFiles \
    --attribute-definitions AttributeName=UploadId,AttributeType=S \
    --key-schema AttributeName=UploadId,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST
```

### 4. Run

```bash
dotnet run
```

API available at `https://localhost:5001`

## API Endpoints

### Upload Zip File
```http
POST /api/fileupload/upload
Content-Type: multipart/form-data

Form Data:
- ZipFile: (file) The zip file to upload
- Description: (string, optional) Description
```

### Get Upload Status
```http
GET /api/fileupload/status/{uploadId}
```

## Example Usage

```bash
# Upload file
curl -X POST "https://localhost:5001/api/fileupload/upload" \
  -F "ZipFile=@/path/to/file.zip" \
  -F "Description=Test upload"

# Check status
curl -X GET "https://localhost:5001/api/fileupload/status/{uploadId}"
```

