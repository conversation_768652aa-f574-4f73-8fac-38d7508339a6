namespace ManualSDCardUploadAPI.Models
{
    public class AwsConfiguration
    {
        public string Profile { get; set; } = "default";
        public string Region { get; set; } = "us-east-1";
        public S3Configuration S3 { get; set; } = new();
        public DynamoDbConfiguration DynamoDB { get; set; } = new();
    }

    public class S3Configuration
    {
        public string BucketName { get; set; } = "manual-sd-card-uploads";
        public int PresignedUrlExpirationMinutes { get; set; } = 60;
    }

    public class DynamoDbConfiguration
    {
        public string TableName { get; set; } = "UploadedFiles";
    }

    public class FileUploadConfiguration
    {
        public long MaxFileSizeBytes { get; set; } = 104857600; // 100MB
        public string[] AllowedExtensions { get; set; } = [".zip"];
        public string UploadPath { get; set; } = "uploads/";
        public int MaxConcurrentUploads { get; set; } = 10;
        public int ProcessingTimeoutMinutes { get; set; } = 30;
    }

    public class SecurityConfiguration
    {
        public bool RequireHttps { get; set; } = true;
        public string[] AllowedOrigins { get; set; } = Array.Empty<string>();
        public bool EnableDetailedErrors { get; set; } = false;
        public int RateLimitRequestsPerMinute { get; set; } = 60;
    }
}
