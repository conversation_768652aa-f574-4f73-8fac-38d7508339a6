using ManualSDCardUploadAPI.Models;
using ManualSDCardUploadAPI.Data;
using Microsoft.EntityFrameworkCore;

namespace ManualSDCardUploadAPI.Services
{
    public class FileStorageService : IFileStorageService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<FileStorageService> _logger;

        public FileStorageService(
            ApplicationDbContext dbContext,
            ILogger<FileStorageService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<bool> SaveFileAsync(UploadedFile uploadedFile)
        {
            _dbContext.UploadedFiles.Add(uploadedFile);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<UploadedFile?> GetFileAsync(string uploadId)
        {
            return await _dbContext.UploadedFiles.FindAsync(uploadId);
        }

        public async Task<byte[]?> GetFileDataAsync(string uploadId)
        {
            var file = await _dbContext.UploadedFiles.FindAsync(uploadId);
            return file?.FileData;
        }
    }
}
