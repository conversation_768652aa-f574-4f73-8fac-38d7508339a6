using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IAwsStorageService _awsStorageService;
        private readonly IZipProcessingService _zipProcessingService;
        private readonly ILogger<FileUploadService> _logger;

        public FileUploadService(
            IAwsStorageService awsStorageService,
            IZipProcessingService zipProcessingService,
            ILogger<FileUploadService> logger)
        {
            _awsStorageService = awsStorageService;
            _zipProcessingService = zipProcessingService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> UploadZipFileAsync(FileUploadRequestDto request)
        {
            var uploadId = Guid.NewGuid().ToString();
            
            try
            {
                _logger.LogInformation("Starting zip file upload process. Upload ID: {UploadId}, File: {FileName}",
                    uploadId, request.ZipFile.FileName);

                // Step 1: Upload the original zip file to S3
                string s3Key;
                using (var fileStream = request.ZipFile.OpenReadStream())
                {
                    s3Key = await _awsStorageService.UploadFileToS3Async(
                        fileStream, 
                        request.ZipFile.FileName, 
                        request.ZipFile.ContentType ?? "application/zip");
                }

                // Step 2: Create initial upload record
                var uploadedFile = new UploadedFile
                {
                    UploadId = uploadId,
                    FileName = request.ZipFile.FileName,
                    FileSizeBytes = request.ZipFile.Length,
                    UploadTimestamp = DateTime.UtcNow,
                    S3Key = s3Key,
                    S3Bucket = "manual-sd-card-uploads", // This should come from configuration
                    Status = "Uploaded",
                    Description = request.Description,
                    DeviceName = request.DeviceName,
                    DeviceModel = request.DeviceModel,
                    CaptureDate = request.CaptureDate,
                    Location = request.Location,
                    Metadata = request.Metadata ?? new Dictionary<string, string>()
                };

                // Save initial metadata
                var saveResult = await _awsStorageService.SaveUploadMetadataAsync(uploadedFile);
                if (!saveResult)
                {
                    _logger.LogError("Failed to save initial upload metadata for Upload ID: {UploadId}", uploadId);
                    return ApiResponseDto<FileUploadResponseDto>.ErrorResponse(
                        "Failed to save upload metadata", "Database operation failed");
                }

                // Step 3: Process the zip file asynchronously
                _ = Task.Run(async () => await ProcessZipFileAsync(uploadId, request.ZipFile));

                // Step 4: Return immediate response
                var response = new FileUploadResponseDto
                {
                    UploadId = uploadId,
                    FileName = request.ZipFile.FileName,
                    FileSizeBytes = request.ZipFile.Length,
                    UploadTimestamp = uploadedFile.UploadTimestamp,
                    S3Key = s3Key,
                    Status = "Uploaded",
                    Description = request.Description,
                    DeviceName = request.DeviceName,
                    DeviceModel = request.DeviceModel,
                    CaptureDate = request.CaptureDate,
                    Location = request.Location,
                    Metadata = request.Metadata
                };

                _logger.LogInformation("Zip file upload initiated successfully. Upload ID: {UploadId}", uploadId);
                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response, 
                    "File uploaded successfully. Processing started in background.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during zip file upload. Upload ID: {UploadId}", uploadId);
                
                // Update status to failed if we have an upload record
                await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Failed", ex.Message);
                
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse(
                    "File upload failed", ex.Message);
            }
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId)
        {
            try
            {
                _logger.LogInformation("Retrieving upload status for Upload ID: {UploadId}", uploadId);

                var uploadedFile = await _awsStorageService.GetUploadMetadataAsync(uploadId);
                if (uploadedFile == null)
                {
                    _logger.LogWarning("Upload not found for Upload ID: {UploadId}", uploadId);
                    return ApiResponseDto<FileUploadResponseDto>.ErrorResponse(
                        "Upload not found", $"No upload found with ID: {uploadId}");
                }

                var response = MapToResponseDto(uploadedFile);
                
                _logger.LogInformation("Successfully retrieved upload status for Upload ID: {UploadId}", uploadId);
                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload status for Upload ID: {UploadId}", uploadId);
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse(
                    "Failed to retrieve upload status", ex.Message);
            }
        }

        public async Task<ApiResponseDto<List<FileUploadResponseDto>>> GetUploadsAsync(string? status = null, string? deviceName = null, int limit = 50)
        {
            try
            {
                _logger.LogInformation("Retrieving uploads list with filters - Status: {Status}, DeviceName: {DeviceName}, Limit: {Limit}",
                    status, deviceName, limit);

                var uploads = await _awsStorageService.GetUploadsAsync(status, deviceName, limit);
                var response = uploads.Select(MapToResponseDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} uploads", response.Count);
                return ApiResponseDto<List<FileUploadResponseDto>>.SuccessResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving uploads list");
                return ApiResponseDto<List<FileUploadResponseDto>>.ErrorResponse(
                    "Failed to retrieve uploads", ex.Message);
            }
        }

        private async Task ProcessZipFileAsync(string uploadId, IFormFile zipFile)
        {
            try
            {
                _logger.LogInformation("Starting background processing for Upload ID: {UploadId}", uploadId);

                // Update status to processing
                await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Processing");

                // Extract and analyze zip contents
                ZipExtractionResult extractionResult;
                using (var fileStream = zipFile.OpenReadStream())
                {
                    extractionResult = await _zipProcessingService.ExtractZipFileAsync(fileStream, zipFile.FileName);
                }

                if (!extractionResult.Success)
                {
                    _logger.LogError("Zip extraction failed for Upload ID: {UploadId}. Error: {Error}",
                        uploadId, extractionResult.ErrorMessage);
                    await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Failed", extractionResult.ErrorMessage);
                    return;
                }

                // Validate zip contents
                var validationResult = await _zipProcessingService.ValidateZipContentsAsync(extractionResult);
                if (!validationResult.IsValid)
                {
                    var errorMessage = string.Join("; ", validationResult.Errors);
                    _logger.LogError("Zip validation failed for Upload ID: {UploadId}. Errors: {Errors}",
                        uploadId, errorMessage);
                    await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Failed", errorMessage);
                    return;
                }

                // Update upload record with extraction results
                var uploadedFile = await _awsStorageService.GetUploadMetadataAsync(uploadId);
                if (uploadedFile != null)
                {
                    uploadedFile.ExtractedFileCount = extractionResult.FileCount;
                    uploadedFile.ExtractedFileNames = extractionResult.ExtractedFiles
                        .Where(f => !f.IsDirectory)
                        .Select(f => f.RelativePath)
                        .ToList();
                    
                    await _awsStorageService.SaveUploadMetadataAsync(uploadedFile);
                }

                // Mark as completed
                await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Completed");

                _logger.LogInformation("Successfully completed processing for Upload ID: {UploadId}. Extracted {FileCount} files.",
                    uploadId, extractionResult.FileCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during background processing for Upload ID: {UploadId}", uploadId);
                await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Failed", ex.Message);
            }
        }

        private static FileUploadResponseDto MapToResponseDto(UploadedFile uploadedFile)
        {
            return new FileUploadResponseDto
            {
                UploadId = uploadedFile.UploadId,
                FileName = uploadedFile.FileName,
                FileSizeBytes = uploadedFile.FileSizeBytes,
                UploadTimestamp = uploadedFile.UploadTimestamp,
                S3Key = uploadedFile.S3Key,
                Status = uploadedFile.Status,
                Description = uploadedFile.Description,
                DeviceName = uploadedFile.DeviceName,
                DeviceModel = uploadedFile.DeviceModel,
                CaptureDate = uploadedFile.CaptureDate,
                Location = uploadedFile.Location,
                ExtractedFileCount = uploadedFile.ExtractedFileCount,
                ExtractedFileNames = uploadedFile.ExtractedFileNames,
                Metadata = uploadedFile.Metadata
            };
        }
    }
}
