using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IAwsStorageService _awsStorageService;
        private readonly IZipProcessingService _zipProcessingService;
        private readonly ILogger<FileUploadService> _logger;

        public FileUploadService(
            IAwsStorageService awsStorageService,
            IZipProcessingService zipProcessingService,
            ILogger<FileUploadService> logger)
        {
            _awsStorageService = awsStorageService;
            _zipProcessingService = zipProcessingService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> UploadZipFileAsync(FileUploadRequestDto request)
        {
            var uploadId = Guid.NewGuid().ToString();

            try
            {
                // Upload file to S3
                string s3Key;
                using (var fileStream = request.ZipFile.OpenReadStream())
                {
                    s3Key = await _awsStorageService.UploadFileToS3Async(
                        fileStream,
                        request.ZipFile.FileName,
                        "application/zip");
                }

                // Save metadata to DynamoDB
                var uploadedFile = new UploadedFile
                {
                    UploadId = uploadId,
                    FileName = request.ZipFile.FileName,
                    FileSizeBytes = request.ZipFile.Length,
                    UploadTimestamp = DateTime.UtcNow,
                    S3Key = s3Key,
                    Status = "Uploaded",
                    Description = request.Description
                };

                await _awsStorageService.SaveUploadMetadataAsync(uploadedFile);

                // Process zip file in background
                _ = Task.Run(async () => await ProcessZipFileAsync(uploadId, request.ZipFile));

                var response = new FileUploadResponseDto
                {
                    UploadId = uploadId,
                    FileName = request.ZipFile.FileName,
                    FileSizeBytes = request.ZipFile.Length,
                    UploadTimestamp = uploadedFile.UploadTimestamp,
                    Status = "Uploaded",
                    Description = request.Description
                };

                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response, "File uploaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file upload");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload failed", ex.Message);
            }
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId)
        {
            try
            {
                var uploadedFile = await _awsStorageService.GetUploadMetadataAsync(uploadId);
                if (uploadedFile == null)
                {
                    return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload not found");
                }

                var response = new FileUploadResponseDto
                {
                    UploadId = uploadedFile.UploadId,
                    FileName = uploadedFile.FileName,
                    FileSizeBytes = uploadedFile.FileSizeBytes,
                    UploadTimestamp = uploadedFile.UploadTimestamp,
                    Status = uploadedFile.Status,
                    Description = uploadedFile.Description,
                    ExtractedFileCount = uploadedFile.ExtractedFileCount
                };

                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload status");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Failed to get status", ex.Message);
            }
        }

        private async Task ProcessZipFileAsync(string uploadId, IFormFile zipFile)
        {
            try
            {
                await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Processing");

                using var fileStream = zipFile.OpenReadStream();
                var extractionResult = await _zipProcessingService.ExtractZipFileAsync(fileStream, zipFile.FileName);

                if (extractionResult.Success)
                {
                    var uploadedFile = await _awsStorageService.GetUploadMetadataAsync(uploadId);
                    if (uploadedFile != null)
                    {
                        uploadedFile.ExtractedFileCount = extractionResult.FileCount;
                        await _awsStorageService.SaveUploadMetadataAsync(uploadedFile);
                    }
                    await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Completed");
                }
                else
                {
                    await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Failed", extractionResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing zip file");
                await _awsStorageService.UpdateUploadStatusAsync(uploadId, "Failed", ex.Message);
            }
        }
    }
}
