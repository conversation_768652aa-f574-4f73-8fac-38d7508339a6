using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IAwsStorageService _awsStorageService;
        private readonly ILogger<FileUploadService> _logger;

        public FileUploadService(
            IAwsStorageService awsStorageService,
            ILogger<FileUploadService> logger)
        {
            _awsStorageService = awsStorageService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> UploadFileAsync(FileUploadRequestDto request)
        {
            var uploadId = Guid.NewGuid().ToString();

            try
            {
                // Upload file to S3
                string s3Key;
                using (var fileStream = request.File.OpenReadStream())
                {
                    s3Key = await _awsStorageService.UploadFileToS3Async(fileStream, request.File.FileName);
                }

                // Save metadata to DynamoDB
                var uploadedFile = new UploadedFile
                {
                    UploadId = uploadId,
                    FileName = request.File.FileName,
                    S3Key = s3Key
                };

                await _awsStorageService.SaveUploadMetadataAsync(uploadedFile);

                var response = new FileUploadResponseDto
                {
                    UploadId = uploadId,
                    FileName = request.File.FileName,
                    S3Key = s3Key
                };

                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response, "File uploaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file upload");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload failed", ex.Message);
            }
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId)
        {
            try
            {
                var uploadedFile = await _awsStorageService.GetUploadMetadataAsync(uploadId);
                if (uploadedFile == null)
                {
                    return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload not found");
                }

                var response = new FileUploadResponseDto
                {
                    UploadId = uploadedFile.UploadId,
                    FileName = uploadedFile.FileName,
                    S3Key = uploadedFile.S3Key
                };

                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload status");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Failed to get status", ex.Message);
            }
        }
    }
}
