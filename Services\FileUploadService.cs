using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Models;

namespace ManualSDCardUploadAPI.Services
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IAwsStorageService _awsStorageService;
        private readonly ILogger<FileUploadService> _logger;

        public FileUploadService(
            IAwsStorageService awsStorageService,
            ILogger<FileUploadService> logger)
        {
            _awsStorageService = awsStorageService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> UploadFileAsync(FileUploadRequestDto request)
        {
            var uploadId = Guid.NewGuid().ToString();

            try
            {
                // TODO upload to AWS
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file upload");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload failed", ex.Message);
            }
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId)
        {
            try
            {
                // use some sort of local tracking, IDK not nessicarly needed
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload status");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Failed to get status", ex.Message);
            }
        }
    }
}
