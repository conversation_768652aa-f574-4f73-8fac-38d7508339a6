using ManualSDCardUploadAPI.DTOs;
using ManualSDCardUploadAPI.Models;
using Microsoft.AspNetCore.Mvc;

namespace ManualSDCardUploadAPI.Services
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<FileUploadService> _logger;

        public FileUploadService(
            IFileStorageService fileStorageService,
            ILogger<FileUploadService> logger)
        {
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> UploadFileAsync(FileUploadRequestDto request)
        {
            var uploadId = Guid.NewGuid().ToString();

            try
            {
                // Read file data into byte array
                byte[] fileData;
                using (var memoryStream = new MemoryStream())
                {
                    await request.File.CopyToAsync(memoryStream);
                    fileData = memoryStream.ToArray();
                }

                // Save file to database
                var uploadedFile = new UploadedFile
                {
                    UploadId = uploadId,
                    FileName = request.File.FileName,
                    FileData = fileData,
                    FileSizeBytes = request.File.Length,
                    UploadTimestamp = DateTime.UtcNow
                };

                await _fileStorageService.SaveFileAsync(uploadedFile);

                var response = new FileUploadResponseDto
                {
                    UploadId = uploadId,
                    FileName = request.File.FileName,
                    FileSizeBytes = request.File.Length,
                    UploadTimestamp = DateTime.UtcNow
                };

                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response, "File uploaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file upload");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload failed", ex.Message);
            }
        }

        public async Task<ApiResponseDto<FileUploadResponseDto>> GetUploadStatusAsync(string uploadId)
        {
            try
            {
                var uploadedFile = await _fileStorageService.GetFileAsync(uploadId);
                if (uploadedFile == null)
                {
                    return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Upload not found");
                }

                var response = new FileUploadResponseDto
                {
                    UploadId = uploadedFile.UploadId,
                    FileName = uploadedFile.FileName,
                    FileSizeBytes = uploadedFile.FileSizeBytes,
                    UploadTimestamp = uploadedFile.UploadTimestamp
                };

                return ApiResponseDto<FileUploadResponseDto>.SuccessResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload status");
                return ApiResponseDto<FileUploadResponseDto>.ErrorResponse("Failed to get status", ex.Message);
            }
        }

        public async Task<IActionResult> DownloadFileAsync(string uploadId)
        {
            try
            {
                var uploadedFile = await _fileStorageService.GetFileAsync(uploadId);
                if (uploadedFile == null)
                {
                    return new NotFoundResult();
                }

                return new FileContentResult(uploadedFile.FileData, "application/octet-stream")
                {
                    FileDownloadName = uploadedFile.FileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file");
                return new StatusCodeResult(500);
            }
        }
    }
}
