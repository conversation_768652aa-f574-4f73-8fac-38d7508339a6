{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Manual-SD-Card-Upload-Rest-API\\ManualSDCardUploadAPI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Manual-SD-Card-Upload-Rest-API\\ManualSDCardUploadAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Manual-SD-Card-Upload-Rest-API\\ManualSDCardUploadAPI.csproj", "projectName": "ManualSDCardUploadAPI", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Manual-SD-Card-Upload-Rest-API\\ManualSDCardUploadAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Manual-SD-Card-Upload-Rest-API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.DynamoDBv2": {"target": "Package", "version": "[4.0.1.7, )"}, "AWSSDK.S3": {"target": "Package", "version": "[4.0.1.7, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}