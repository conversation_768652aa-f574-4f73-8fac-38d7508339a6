using System.ComponentModel.DataAnnotations;

namespace ManualSDCardUploadAPI.DTOs
{
    public class FileUploadRequestDto
    {
        [Required]
        public IFormFile ZipFile { get; set; } = null!;

        [StringLength(255)]
        public string? Description { get; set; }

        [StringLength(100)]
        public string? DeviceName { get; set; }

        [StringLength(50)]
        public string? DeviceModel { get; set; }

        public DateTime? CaptureDate { get; set; }

        [StringLength(100)]
        public string? Location { get; set; }

        public Dictionary<string, string>? Metadata { get; set; }
    }
}
