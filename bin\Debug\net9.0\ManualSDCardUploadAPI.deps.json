{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"ManualSDCardUploadAPI/1.0.0": {"dependencies": {"AWSSDK.DynamoDBv2": "*******", "AWSSDK.S3": "*******", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Swashbuckle.AspNetCore": "9.0.1"}, "runtime": {"ManualSDCardUploadAPI.dll": {}}}, "AWSSDK.Core/********": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "AWSSDK.DynamoDBv2/*******": {"dependencies": {"AWSSDK.Core": "********"}, "runtime": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AWSSDK.S3/*******": {"dependencies": {"AWSSDK.Core": "********"}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/9.0.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "8.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.1", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.1"}}, "Swashbuckle.AspNetCore.Swagger/9.0.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.1"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.1": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}}}}, "libraries": {"ManualSDCardUploadAPI/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/********": {"type": "package", "serviceable": true, "sha512": "sha512-y7J/hTTtCaOFtcuHKmcNyiB6SUpOU7hH9N0HLa6ajvZoF0QdCOmOlY3r3pbddVWpZoFttOBng064HEvKOX7rtw==", "path": "awssdk.core/********", "hashPath": "awssdk.core.********.nupkg.sha512"}, "AWSSDK.DynamoDBv2/*******": {"type": "package", "serviceable": true, "sha512": "sha512-ZdBXcqGAZvEshAeyX0WvdinVSQsf2DFOr2b17mTJksUpoQ2i186IGcIEsXAdlxpn2fiDowzn9v+DLL2tcszBHg==", "path": "awssdk.dynamodbv2/*******", "hashPath": "awssdk.dynamodbv2.*******.nupkg.sha512"}, "AWSSDK.S3/*******": {"type": "package", "serviceable": true, "sha512": "sha512-wZvlAVLgTPNQlFvHhacE3EOgoATx+hPKYijbubxoY5zdKm2EjKd8ucMZTvGJIzsbV7lr4aRfuu8dALCI52CH1Q==", "path": "awssdk.s3/*******", "hashPath": "awssdk.s3.*******.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-MOJ4DG1xd3NlWMYh+JdGNT9uvBtEk1XQU/FQlpNZFlAzM8t0oB5IimvnGlnK7jmyY4vQagLPB1xw1HjJ8CHrZg==", "path": "microsoft.aspnetcore.openapi/9.0.6", "hashPath": "microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jDM3a95WerM8g6IcMiBXq1qRS9dqmEUpgnCk2DeMWpPkYtp1ia+CkXabOnK93JmhVlUmv8l9WMPsCSUm+WqkIA==", "path": "microsoft.extensions.apidescription.server/8.0.0", "hashPath": "microsoft.extensions.apidescription.server.8.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lmaz0juKGq8VgeCOki98OUVdEH7rvgBST0QrlWXNOxBl7ujNWyqdqj2SMAdgpQfTtBlSQUTHRslRM+0j7El5tA==", "path": "swashbuckle.aspnetcore/9.0.1", "hashPath": "swashbuckle.aspnetcore.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4/9tBsbeCN+ewwqjEBPq3BszNE9tOvA1iDogwT7qW7L0Uh942IozhtF9VaICD70XyZIlKNiHjc2Vt5QE09P4nw==", "path": "swashbuckle.aspnetcore.swagger/9.0.1", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Za5w1NfLMF0Tt4GTgC491wfmuuMwNw8A5nwZuytpBd4UXYZxkwNJa3QnRYGbUQD9MjLzQCYnKc8rMrr5LglW6A==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UuU+RvZbJZmnciVzi8G0jb3c6xeA4O8pQGrxl30eCK3pRNvurzZgS6shEei27q9wgvp0/UrTUNUM/a1kSlTfzw==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.1.nupkg.sha512"}}}