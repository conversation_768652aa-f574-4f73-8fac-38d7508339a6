using ManualSDCardUploadAPI.Models;
using System.IO.Compression;

namespace ManualSDCardUploadAPI.Services
{
    public class ZipProcessingService : IZipProcessingService
    {
        private readonly ILogger<ZipProcessingService> _logger;

        public ZipProcessingService(ILogger<ZipProcessingService> logger, IConfiguration configuration)
        {
            _logger = logger;
        }

        public async Task<ZipExtractionResult> ExtractZipFileAsync(Stream zipFileStream, string fileName)
        {
            var result = new ZipExtractionResult();

            try
            {
                using var archive = new ZipArchive(zipFileStream, ZipArchiveMode.Read, leaveOpen: true);

                var fileCount = 0;
                var totalSize = 0L;

                foreach (var entry in archive.Entries)
                {
                    if (!entry.FullName.EndsWith("/")) // Skip directories
                    {
                        fileCount++;
                        totalSize += entry.Length;
                    }
                }

                result.FileCount = fileCount;
                result.TotalSizeBytes = totalSize;
                result.Success = true;

                _logger.LogInformation("Extracted {FileCount} files from {FileName}", fileCount, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting zip file: {FileName}", fileName);
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

    }
}
 
