using ManualSDCardUploadAPI.Models;
using System.IO.Compression;
using System.Text;

namespace ManualSDCardUploadAPI.Services
{
    public class ZipProcessingService : IZipProcessingService
    {
        private readonly ILogger<ZipProcessingService> _logger;
        private readonly IConfiguration _configuration;

        // Security limits
        private const long MaxExtractedSize = 1024 * 1024 * 1024; // 1GB
        private const int MaxFileCount = 10000;
        private const int MaxPathLength = 260;
        private readonly HashSet<string> _dangerousExtensions = new(StringComparer.OrdinalIgnoreCase)
        {
            ".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".vbs", ".js", ".jar", ".msi"
        };

        public ZipProcessingService(ILogger<ZipProcessingService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<ZipExtractionResult> ExtractZipFileAsync(Stream zipFileStream, string fileName)
        {
            var result = new ZipExtractionResult();

            try
            {
                _logger.LogInformation("Starting extraction of zip file: {FileName}", fileName);

                using var archive = new ZipArchive(zipFileStream, ZipArchiveMode.Read, leaveOpen: true);
                
                var totalExtractedSize = 0L;
                var fileCount = 0;

                foreach (var entry in archive.Entries)
                {
                    // Security check: prevent zip bombs
                    if (fileCount >= MaxFileCount)
                    {
                        result.Warnings.Add($"Maximum file count ({MaxFileCount}) exceeded. Some files were skipped.");
                        break;
                    }

                    if (totalExtractedSize >= MaxExtractedSize)
                    {
                        result.Warnings.Add($"Maximum extraction size ({MaxExtractedSize:N0} bytes) exceeded. Some files were skipped.");
                        break;
                    }

                    // Security check: prevent directory traversal
                    if (IsPathTraversal(entry.FullName))
                    {
                        result.Warnings.Add($"Skipped potentially dangerous path: {entry.FullName}");
                        continue;
                    }

                    // Security check: path length
                    if (entry.FullName.Length > MaxPathLength)
                    {
                        result.Warnings.Add($"Skipped file with path too long: {entry.FullName}");
                        continue;
                    }

                    var zipContent = new ZipFileContent
                    {
                        FileName = Path.GetFileName(entry.FullName),
                        RelativePath = entry.FullName,
                        FileSizeBytes = entry.Length,
                        FileExtension = Path.GetExtension(entry.FullName).ToLowerInvariant(),
                        LastModified = entry.LastWriteTime.DateTime,
                        IsDirectory = entry.FullName.EndsWith("/") || string.IsNullOrEmpty(entry.Name)
                    };

                    // Only extract file content for non-directories and reasonably sized files
                    if (!zipContent.IsDirectory && entry.Length <= 50 * 1024 * 1024) // 50MB limit per file
                    {
                        using var entryStream = entry.Open();
                        using var memoryStream = new MemoryStream();
                        await entryStream.CopyToAsync(memoryStream);
                        zipContent.FileContent = memoryStream.ToArray();
                    }

                    result.ExtractedFiles.Add(zipContent);
                    totalExtractedSize += entry.Length;
                    fileCount++;
                }

                result.TotalSizeBytes = totalExtractedSize;
                result.FileCount = fileCount;
                result.Success = true;

                _logger.LogInformation("Successfully extracted {FileCount} files from {FileName}, total size: {TotalSize:N0} bytes",
                    fileCount, fileName, totalExtractedSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting zip file: {FileName}", fileName);
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        public async Task<ValidationResult> ValidateZipContentsAsync(ZipExtractionResult extractionResult)
        {
            var result = new ValidationResult { IsValid = true };

            await Task.Run(() =>
            {
                // Check for dangerous file types
                var dangerousFiles = extractionResult.ExtractedFiles
                    .Where(f => _dangerousExtensions.Contains(f.FileExtension))
                    .ToList();

                if (dangerousFiles.Any())
                {
                    result.Warnings.AddRange(dangerousFiles.Select(f => 
                        $"Potentially dangerous file type detected: {f.RelativePath}"));
                }

                // Check for suspicious file patterns
                var suspiciousFiles = extractionResult.ExtractedFiles
                    .Where(f => f.FileName.Contains("..") || f.FileName.StartsWith("."))
                    .ToList();

                if (suspiciousFiles.Any())
                {
                    result.Warnings.AddRange(suspiciousFiles.Select(f => 
                        $"Suspicious file name pattern: {f.RelativePath}"));
                }

                // Check for empty files
                var emptyFiles = extractionResult.ExtractedFiles
                    .Where(f => !f.IsDirectory && f.FileSizeBytes == 0)
                    .ToList();

                if (emptyFiles.Count > 10) // More than 10 empty files might be suspicious
                {
                    result.Warnings.Add($"Large number of empty files detected: {emptyFiles.Count}");
                }

                // Validate overall structure
                if (extractionResult.FileCount == 0)
                {
                    result.IsValid = false;
                    result.Errors.Add("Zip file contains no extractable files");
                }

                if (extractionResult.TotalSizeBytes == 0)
                {
                    result.Warnings.Add("All extracted files appear to be empty");
                }
            });

            return result;
        }

        public ZipContentSummary GetContentSummary(ZipExtractionResult extractionResult)
        {
            var summary = new ZipContentSummary
            {
                TotalFiles = extractionResult.ExtractedFiles.Count(f => !f.IsDirectory),
                TotalDirectories = extractionResult.ExtractedFiles.Count(f => f.IsDirectory),
                TotalSizeBytes = extractionResult.TotalSizeBytes
            };

            // File type analysis
            summary.FileTypeCount = extractionResult.ExtractedFiles
                .Where(f => !f.IsDirectory)
                .GroupBy(f => f.FileExtension)
                .ToDictionary(g => g.Key, g => g.Count());

            // Largest files (top 5)
            summary.LargestFiles = extractionResult.ExtractedFiles
                .Where(f => !f.IsDirectory)
                .OrderByDescending(f => f.FileSizeBytes)
                .Take(5)
                .Select(f => $"{f.RelativePath} ({f.FileSizeBytes:N0} bytes)")
                .ToList();

            // Date range
            var filesWithDates = extractionResult.ExtractedFiles
                .Where(f => f.LastModified.HasValue)
                .ToList();

            if (filesWithDates.Any())
            {
                summary.OldestFileDate = filesWithDates.Min(f => f.LastModified);
                summary.NewestFileDate = filesWithDates.Max(f => f.LastModified);
            }

            return summary;
        }

        private static bool IsPathTraversal(string path)
        {
            // Check for directory traversal patterns
            return path.Contains("..") || 
                   path.StartsWith("/") || 
                   path.Contains("\\..\\") || 
                   path.Contains("/../") ||
                   Path.IsPathRooted(path);
        }
    }
}
